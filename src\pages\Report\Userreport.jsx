import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Form, Button, Table, Card, Spinner, Badge, Dropdown, Toast } from 'react-bootstrap';
import { FaCalendarAlt, FaUserTie, FaChartBar, FaFileExport, FaSearch, FaFilter, FaFileExcel, FaFilePdf, FaChevronRight, FaChevronDown } from 'react-icons/fa';
import API from "../../CustomHooks/MasterApiHooks/api";

const UserReport = () => {
    const [startDate, setStartDate] = useState(() => {
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        return `${yyyy}-${mm}-${dd}`;
    });
    const [endDate, setEndDate] = useState("");
    const [reportType, setReportType] = useState('details');
    const [selectedUser, setSelectedUser] = useState('all');
    const [users, setUsers] = useState([]);
    const [userReportData, setUserReportData] = useState([]);
    const [summaryData, setSummaryData] = useState([]); // New state for summary
    const [isLoading, setIsLoading] = useState(false);
    const [showTable, setShowTable] = useState(false);
    const [expandedRows, setExpandedRows] = useState(new Set());
    const [showToast, setShowToast] = useState(false);
    const [toastMessage, setToastMessage] = useState('');

    // State for processes data
    const [processes, setProcesses] = useState([]);

    // Helper to format date as dd-MM-yyyy
    const formatDateForApi = (dateStr) => {
        const d = new Date(dateStr);
        const day = String(d.getDate()).padStart(2, '0');
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const year = d.getFullYear();
        return `${day}-${month}-${year}`;
    };

    useEffect(() => {
        // Fetch users and processes from API
        const fetchInitialData = async () => {
            try {
                // Fetch users
                const userResponse = await API.get('/User');
                const userOptions = [
                    { id: 'all', name: 'All Users' },
                    ...userResponse.data.map(user => ({
                        id: user.id,
                        name: user.name || user.userName
                    }))
                ];
                setUsers(userOptions);

                // Fetch processes
                const processResponse = await API.get('/Process');
                setProcesses(processResponse.data);
            } catch (error) {
                console.error('Error fetching initial data:', error);
                setUsers([
                    { id: 'all', name: 'All Users' }
                ]);
                setProcesses([]);
            }
        };
        fetchInitialData();
    }, []);

    // Fetch users when report type changes to details
    useEffect(() => {
        if (reportType === 'details') {
            const fetchUsers = async () => {
                try {
                    const userResponse = await API.get('/User');
                    const userOptions = [
                        { id: 'all', name: 'All Users' },
                        ...userResponse.data.map(user => ({
                            id: user.id,
                            name: user.name || user.userName
                        }))
                    ];
                    setUsers(userOptions);
                } catch (error) {
                    console.error('Error fetching users for details:', error);
                    setUsers([
                        { id: 'all', name: 'All Users' }
                    ]);
                }
            };
            fetchUsers();
        }
    }, [reportType]);

    // Removed summary fetch from useEffect. Now only fetch on button click.

    // Helper function to get process name by ID
    const getProcessName = (processId) => {
        const process = processes.find(p => p.id === processId);
        return process ? process.name : `Process ${processId}`;
    };

    const handleViewReport = async () => {
        setIsLoading(true);
        try {
            if (reportType === 'summary') {
                const formattedDate = formatDateForApi(startDate);
                const response = await API.get(`/Reports/User-Wise-Summary?date=${formattedDate}`);
                const summary = response.data.userWiseSummary || [];
                setSummaryData(summary);
                setShowTable(true);
                // Filter users for dropdown based on summary supervisors (only for summary mode)
                const summaryUsers = summary.map(item => ({
                    id: item.supervisor,
                    name: item.supervisor
                }));
                setUsers([{ id: 'all', name: 'All Users' }, ...summaryUsers]);
            } else if (reportType === 'details') {
                // Build API parameters
                const params = new URLSearchParams();

                // Only add userId if a specific user is selected and it's a valid integer
                if (selectedUser !== 'all' && !isNaN(parseInt(selectedUser))) {
                    params.append('userId', parseInt(selectedUser));
                }

                // For single date selection, use date parameter
                if (startDate && !endDate) {
                    params.append('date', formatDateForApi(startDate));
                } else if (startDate && endDate) {
                    // For date range selection, use startDate and endDate parameters
                    params.append('startDate', formatDateForApi(startDate));
                    params.append('endDate', formatDateForApi(endDate));
                }

                params.append('page', '1');
                params.append('pageSize', '1000'); // Get all data

                const response = await API.get(`/Reports/User-Wise?${params.toString()}`);
                const apiData = response.data.groupWiseSummary || [];

                // Transform API data to match table structure
                const transformedData = apiData.map(group => ({
                    key: `group-${group.groupId}`,
                    group: group.groupName,
                    paperCount: group.catchCountInPaper,
                    paperQty: group.quantitySumInPaper,
                    bookletCount: group.catchCountInBooklet,
                    bookletQty: group.quantitySumInBooklet,
                    process: group.processIds ? group.processIds.map(id => getProcessName(id)) : []
                }));

                setUserReportData(transformedData);
                setShowTable(true);
            }
        } catch (error) {
            console.error('Error fetching report data:', error);
            // Show toast notification for no data
            if (error.response && error.response.status === 404) {
                setToastMessage('No data found for the selected criteria.');
                setShowToast(true);
                setUserReportData([]);
                setSummaryData([]);
                setShowTable(true);
            } else {
                setToastMessage('Error fetching report data. Please try again.');
                setShowToast(true);
            }
        } finally {
            setIsLoading(false);
        }
    };

    const toggleRow = (itemKey) => {
        const newExpandedRows = new Set(expandedRows);
        if (newExpandedRows.has(itemKey)) {
            newExpandedRows.delete(itemKey);
        } else {
            newExpandedRows.add(itemKey);
        }
        setExpandedRows(newExpandedRows);
    };

    const renderProcessTags = (processes) => {
        if (!processes || processes.length === 0) return null;
        return (
            <div className="d-flex flex-wrap gap-1">
                {processes.map((process, index) => (
                    <Badge key={index} bg="warning" text="dark" className="me-1">
                        {process}
                    </Badge>
                ))}
            </div>
        );
    };

    const renderTableRow = (item, level = 0) => {
        const hasChildren = item.children && item.children.length > 0;
        const isExpanded = expandedRows.has(item.key);
        
        const getRowClass = (level) => {
            if (level === 0) return '';
            if (level === 1) return 'table-secondary';
            if (level === 2) return 'table-light';
            return 'table-light';
        };
        
        return (
            <React.Fragment key={item.key}>
                <tr 
                    className={`${hasChildren ? 'clickable-row' : ''} ${getRowClass(level)}`}
                    onClick={hasChildren ? () => toggleRow(item.key) : undefined}
                    style={{
                        cursor: hasChildren ? 'pointer' : 'default',
                        paddingLeft: `${level * 20}px`
                    }}
                >
                    <td className="text-center" style={{ paddingLeft: `${level * 20}px` }}>
                        <div className="d-flex align-items-center justify-content-center">
                            {hasChildren && (
                                <span className="me-2 text-primary">
                                    {isExpanded ? <FaChevronDown /> : <FaChevronRight />}
                                </span>
                            )}
                            <strong>{item.group}</strong>
                        </div>
                    </td>
                    <td className="text-center">{item.paperCount || '-'}</td>
                    <td className="text-center">{item.paperQty || '-'}</td>
                    <td className="text-center">{item.bookletCount || '-'}</td>
                    <td className="text-center">{item.bookletQty || '-'}</td>
                    <td className="text-center">{renderProcessTags(item.process)}</td>
                </tr>
                {hasChildren && isExpanded && item.children.map(child => renderTableRow(child, level + 1))}
            </React.Fragment>
        );
    };

    // Filtered summary data based on selected user
    const filteredSummaryData = selectedUser === 'all'
        ? summaryData
        : summaryData.filter(item => item.supervisor === selectedUser);

    return (
        <>
           
            <Row>
                <Col xs={12}>
                   
                    {/* Filters Section */}
                    <Row className="mb-4">
                        <Col xs={12} >
                            
                                
                                    <Row className="g-3">
                                        <Col xs={12} sm={6} md={3}lg={1}>
                                            <Form.Group>
                                                <Form.Label className="small fw-semibold mb-1">
                                                    <FaCalendarAlt className="me-1" />
                                                    From 
                                                </Form.Label>
                                                <Form.Control
                                                    type="date"
                                                    value={startDate}
                                                    placeholder="Select date"
                                                    onChange={(e) => setStartDate(e.target.value)}
                                                    size="sm"
                                                />
                                            </Form.Group>
                                        </Col>
                                        <Col xs={12} sm={6} md={3}lg={1}>
                                            <Form.Group>
                                                <Form.Label className="small fw-semibold mb-1">
                                                    <FaCalendarAlt className="me-1" />
                                                    To 
                                                </Form.Label>
                                                <Form.Control
                                                    type="date"
                                                    value={endDate}
                                                    placeholder="Select date"
                                                    onChange={(e) => setEndDate(e.target.value)}
                                                    size="sm"
                                                />
                                            </Form.Group>
                                        </Col>
                                        <Col xs={12} sm={6} md={2}lg={1}>
                                            <Form.Group>
                                                <Form.Label className="small fw-semibold mb-1">
                                                    <FaChartBar className="me-1" />
                                                    Report Type
                                                </Form.Label>
                                                <Form.Select
                                                    value={reportType}
                                                    onChange={(e) => setReportType(e.target.value)}
                                                    size="sm"
                                                >
                                                    <option value="summary"> Summary</option>
                                                    <option value="details"> Details</option>
                                                </Form.Select>
                                            </Form.Group>
                                        </Col>
                                        <Col xs={12} sm={6} md={2}lg={1}>
                                            <Form.Group>
                                                <Form.Label className="small fw-semibold mb-1">
                                                    <FaUserTie className="me-1" />
                                                    Select User
                                                </Form.Label>
                                                <Form.Select
                                                    value={selectedUser}
                                                    onChange={(e) => setSelectedUser(e.target.value)}
                                                    size="sm"
                                                >
                                                    {users.map(user => (
                                                        <option key={user.id} value={user.id}>
                                                            {user.name}
                                                        </option>
                                                    ))}
                                                </Form.Select>
                                            </Form.Group>
                                        </Col>
                                        <Col xs={12} sm={12} md={2} lg={1} className="d-flex align-items-end">
                                            <Button
                                                variant="primary"
                                                onClick={handleViewReport}
                                                disabled={isLoading}
                                                size="sm"
                                                className="w-100"
                                            >
                                                {isLoading ? (
                                                    <>
                                                        <Spinner animation="border" size="sm" className="me-2" />
                                                        Loading...
                                                    </>
                                                ) : (
                                                    <>
                                                        
                                                        View Report
                                                    </>
                                                )}
                                            </Button>
                                        </Col>
                                    </Row>
                                
                            
                        </Col>
                    </Row>

                    {/* Summary Table */}
                    {showTable && reportType === 'summary' && (
                        <Row className="mb-4">
                            <Col xs={12} lg={12}>
                                <Card>
                                    <Card.Body>
                                        <Row className="mb-3">
                                            <Col xs={12} className="d-flex justify-content-end">
                                                <Dropdown align="end">
                                                    <Dropdown.Toggle
                                                        variant="outline-primary"
                                                        size="sm"
                                                        id="export-dropdown"
                                                        style={{
                                                            width: 80,
                                                            height: 40,
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            padding: 0
                                                        }}
                                                    >
                                                        <FaFileExport style={{ fontSize: '1.3rem', margin: 0 }} />
                                                    </Dropdown.Toggle>
                                                    <Dropdown.Menu style={{ minWidth: 60 }}>
                                                        <Dropdown.Item
                                                            href="#excel"
                                                            style={{
                                                                display: 'flex',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                padding: 8
                                                            }}
                                                        >
                                                            <FaFileExcel style={{ color: '#217346', fontSize: '1.5rem' }} />
                                                        </Dropdown.Item>
                                                        <Dropdown.Item
                                                            href="#pdf"
                                                            style={{
                                                                display: 'flex',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                padding: 8
                                                            }}
                                                        >
                                                            <FaFilePdf style={{ color: '#d32f2f', fontSize: '1.5rem' }} />
                                                        </Dropdown.Item>
                                                    </Dropdown.Menu>
                                                </Dropdown>
                                            </Col>
                                        </Row>
                                        <Row>
                                            <Col xs={12} className="d-flex justify-content-center">
                                                <div className="d-flex justify-content-center">
                                                    <Table bordered size="lg" className="mb-2">
                                                        <thead className="table-light">
                                                            <tr>
                                                                <th className="text-center" style={{ fontSize: '0.9rem' }}>Supervisor</th>
                                                                <th className="text-center" style={{ fontSize: '0.9rem' }}>Catch Count</th>
                                                                <th className="text-center" style={{ fontSize: '0.9rem' }}>Total Quantity</th>
                                                                <th className="text-center" style={{ fontSize: '0.9rem' }}>Group Count</th>
                                                                <th className="text-center" style={{ fontSize: '0.9rem' }}>Project Count</th>
                                                                <th className="text-center" style={{ fontSize: '0.9rem' }}>Lot Count</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {filteredSummaryData.length === 0 ? (
                                                                <tr><td colSpan="6" className="text-center">No summary data found for this date.</td></tr>
                                                            ) : (
                                                                filteredSummaryData.map((item, idx) => (
                                                                    <tr key={idx}>
                                                                        <td className="text-center">{item.supervisor}</td>
                                                                        <td className="text-center">{item.catchCount}</td>
                                                                        <td className="text-center">{item.totalQuantity}</td>
                                                                        <td className="text-center">{item.countOfGroupIds}</td>
                                                                        <td className="text-center">{item.countIfProjectIds}</td>
                                                                        <td className="text-center">{item.countOfLotNo}</td>
                                                                    </tr>
                                                                ))
                                                            )}
                                                        </tbody>
                                                    </Table>
                                                </div>
                                            </Col>
                                        </Row>
                                    </Card.Body>
                                </Card>
                            </Col>
                        </Row>
                    )}

                    {/* Details Table */}
                    {showTable && reportType === 'details' && (
                        <Row>
                            <Col xs={12} lg={12}>
                                <Card>
                                    <Card.Body>
                                        <Row className="mb-3">
                                            <Col xs={12} className="d-flex justify-content-end">
                                                <Dropdown align="end">
                                                    <Dropdown.Toggle
                                                       
                                                        size="sm"
                                                        id="export-dropdown"
                                                        style={{
                                                            width: 90,
                                                            height: 40,
                                                            
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            justifyContent: 'center',
                                                            padding: 0
                                                        }}
                                                    >
                                                        <FaFileExport style={{ fontSize: '1.3rem', margin: 0 }} />
                                                    </Dropdown.Toggle>
                                                    <Dropdown.Menu style={{ minWidth: 60 }}>
                                                        <Dropdown.Item
                                                            href="#excel"
                                                            style={{
                                                                display: 'flex',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                padding: 8
                                                            }}
                                                        >
                                                            <FaFileExcel style={{ color: '#217346', fontSize: '1.5rem' }} />
                                                        </Dropdown.Item>
                                                        <Dropdown.Item
                                                            href="#pdf"
                                                            style={{
                                                                display: 'flex',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                padding: 8
                                                            }}
                                                        >
                                                            <FaFilePdf style={{ color: '#d32f2f', fontSize: '1.5rem' }} />
                                                        </Dropdown.Item>
                                                    </Dropdown.Menu>
                                                </Dropdown>
                                            </Col>
                                        </Row>
                                        <Row>
                                            <Col xs={12}>
                                                <div className="table-responsive">
                                                    <Table bordered striped hover className="mb-0">
                                                        <thead className="bg-primary text-white">
                                                            <tr>
                                                                <th className="text-center"><strong>Groups</strong></th>
                                                                <th colSpan="2" className="text-center">
                                                                    <div>Paper</div>
                                                                </th>
                                                                <th colSpan="2" className="text-center">
                                                                    <div>Booklet</div>
                                                                </th>
                                                                <th className="text-center"><strong>Process</strong></th>
                                                            </tr>
                                                            <tr>
                                                                <th className="text-center"></th>
                                                                <th className="text-center"><strong>Catch</strong></th>
                                                                <th className="text-center"><strong>Quantity</strong></th>
                                                                <th className="text-center"><strong>Catch</strong></th>
                                                                <th className="text-center"><strong>Quantity</strong></th>
                                                                <th className="text-center"></th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {userReportData.length === 0 ? (
                                                                <tr>
                                                                    <td colSpan="6" className="text-center py-4">
                                                                        No data found for the selected criteria.
                                                                    </td>
                                                                </tr>
                                                            ) : (
                                                                userReportData.map(item => renderTableRow(item))
                                                            )}
                                                        </tbody>
                                                    </Table>
                                                </div>
                                            </Col>
                                        </Row>
                                    </Card.Body>
                                </Card>
                            </Col>
                        </Row>
                    )}

                    {/* No Data Message */}
                    {!showTable && !isLoading && (
                        <Row>
                            <Col xs={12}>
                                <Card>
                                    <Card.Body className="text-center py-5">
                                        <FaChartBar style={{ fontSize: '3rem', color: '#4a90e2', marginBottom: '1rem' }} />
                                        <h5>No Report Data</h5>
                                        <p className="text-muted">Select filters and click "View Report" to generate the user report.</p>
                                    </Card.Body>
                                </Card>
                            </Col>
                        </Row>
                    )}
                </Col>
            </Row>

            {/* Toast Notification */}
            <div className="position-fixed top-0 end-0 p-3" style={{ zIndex: 1050 }}>
                <Toast
                    show={showToast}
                    onClose={() => setShowToast(false)}
                    delay={3000}
                    autohide
                    bg="info"
                >
                    <Toast.Body className="text-white">
                        {toastMessage}
                    </Toast.Body>
                </Toast>
            </div>
        </>
    );
};

export default UserReport;
