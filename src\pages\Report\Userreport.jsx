import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Form, Button, Table, Card, Spinner, Dropdown, Toast } from 'react-bootstrap';
import { FaCalendarAlt, FaUserTie, FaChartBar, FaFileExport, FaSearch, FaFileExcel, FaFilePdf, FaChevronRight, FaChevronDown } from 'react-icons/fa';
import API from "../../CustomHooks/MasterApiHooks/api";

const Userreport = () => {
    // State variables
    const [startDate, setStartDate] = useState(() => {
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        return `${yyyy}-${mm}-${dd}`;
    });
    const [endDate, setEndDate] = useState("");
    const [reportType, setReportType] = useState('summary');
    const [selectedUser, setSelectedUser] = useState('all');
    const [users, setUsers] = useState([]);
    const [allUsers, setAllUsers] = useState([]); // Store all users for ID mapping
    const [processes, setProcesses] = useState([]); // Store processes for name mapping
    const [userReportData, setUserReportData] = useState([]);
    const [expandedGroups, setExpandedGroups] = useState(new Set()); // Track expanded groups
    const [projectData, setProjectData] = useState({}); // Store project data for each group
    const [loadingProjects, setLoadingProjects] = useState(new Set()); // Track loading state for projects
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingUsers, setIsLoadingUsers] = useState(false);
    const [showTable, setShowTable] = useState(false);
    const [showToast, setShowToast] = useState(false);
    const [toastMessage, setToastMessage] = useState('');

    // Format date for API (DD-MM-YYYY format)
    const formatDateForApi = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    };

    // Fetch all users and processes for mapping on component mount
    useEffect(() => {
        const fetchInitialData = async () => {
            try {
                // Fetch all users for ID mapping
                const userResponse = await API.get('/User');

                // Create a mapping of userName to userId
                const usersData = userResponse.data;
                console.log('All users data:', usersData);
                console.log('Sample user structure:', usersData[0]);

                setAllUsers(usersData);

                // Fetch all processes for name mapping
                const processResponse = await API.get('/Processes');
                setProcesses(processResponse.data);
            } catch (error) {
                console.error('Error fetching initial data:', error);
                setAllUsers([]);
                setProcesses([]);
            }
        };

        fetchInitialData();
    }, []);

    // Fetch users based on selected date
    useEffect(() => {
        const fetchUsersForDate = async () => {
            if (!startDate) {
                setUsers([{ id: 'all', name: 'All Users' }]);
                setIsLoadingUsers(false);
                return;
            }

            setIsLoadingUsers(true);
            try {
                // Fetch user summary data for the selected date to get available supervisors
                const params = new URLSearchParams();
                params.append('date', formatDateForApi(startDate));

                const response = await API.get(`/Reports/User-Wise-Summary?${params.toString()}`);
                const summaryData = response.data.userWiseSummary || [];

                // Extract unique supervisors from the summary data and map to user IDs
                const availableUsers = summaryData.map(item => {
                    // Find the user by matching userName with supervisor
                    const user = allUsers.find(u => u.userName === item.supervisor);

                    // Log for debugging
                    console.log(`Mapping supervisor ${item.supervisor} to user:`, user);

                    // Determine the actual user ID to use for API calls
                    let actualUserId = null;
                    if (user) {
                        // Try different possible ID fields
                        actualUserId = user.id || user.userId || user.ID || user.UserId;
                    }

                    return {
                        id: item.supervisor, // Use supervisor name as id for selection
                        name: item.supervisor,
                        userId: actualUserId, // Store the actual numeric user ID
                        originalUser: user // Store the full user object for debugging
                    };
                });

                // Add "All Users" option at the beginning
                const userOptions = [
                    { id: 'all', name: 'All Users', userId: null },
                    ...availableUsers
                ];

                setUsers(userOptions);

                // Reset selected user to 'all' when date changes
                setSelectedUser('all');

            } catch (error) {
                console.error('Error fetching users for date:', error);
                setUsers([{ id: 'all', name: 'All Users', userId: null }]);
                setSelectedUser('all');
            } finally {
                setIsLoadingUsers(false);
            }
        };

        // Only fetch users for date if allUsers is loaded
        if (allUsers.length > 0) {
            fetchUsersForDate();
        }
    }, [startDate, allUsers]); // Re-run when startDate or allUsers changes

    // Helper function to get process name by ID
    const getProcessName = (processId) => {
        if (!processId) return 'N/A';
        const process = processes.find(p => p.id === processId);
        return process ? process.name : `Process ${processId}`;
    };

    // Function to handle group row expansion
    const handleGroupExpansion = async (groupId) => {
        const newExpandedGroups = new Set(expandedGroups);

        if (expandedGroups.has(groupId)) {
            // Collapse the group
            newExpandedGroups.delete(groupId);
            setExpandedGroups(newExpandedGroups);
        } else {
            // Expand the group and fetch project data
            newExpandedGroups.add(groupId);
            setExpandedGroups(newExpandedGroups);

            // Check if we already have project data for this group
            if (!projectData[groupId]) {
                await fetchProjectData(groupId);
            }
        }
    };

    // Function to fetch project data for a specific group
    const fetchProjectData = async (groupId) => {
        const newLoadingProjects = new Set(loadingProjects);
        newLoadingProjects.add(groupId);
        setLoadingProjects(newLoadingProjects);

        try {
            const params = new URLSearchParams();

            // Add the same parameters as the main details call
            if (selectedUser !== 'all') {
                const selectedUserObj = users.find(u => u.id === selectedUser);
                if (selectedUserObj && selectedUserObj.userId) {
                    params.append('userId', selectedUserObj.userId);
                } else {
                    const matchedUser = allUsers.find(u => u.userName === selectedUser);
                    if (matchedUser) {
                        const userId = matchedUser.id || matchedUser.userId || matchedUser.ID || matchedUser.UserId;
                        if (userId) {
                            params.append('userId', userId);
                        }
                    }
                }
            }

            if (startDate && !endDate) {
                params.append('date', formatDateForApi(startDate));
            } else if (startDate && endDate) {
                params.append('startDate', formatDateForApi(startDate));
                params.append('endDate', formatDateForApi(endDate));
            }

            // Add groupId parameter for project-level data
            params.append('groupId', groupId);
            params.append('page', '1');
            params.append('pageSize', '1000');

            const response = await API.get(`/Reports/User-Wise?${params.toString()}`);
            const projectsData = response.data.projectWiseSummary || [];

            // Transform project data to include process names
            const transformedProjects = projectsData.map(project => ({
                ...project,
                processNames: project.processIds ? project.processIds.map(id => getProcessName(id)) : []
            }));

            // Store project data for this group
            setProjectData(prev => ({
                ...prev,
                [groupId]: transformedProjects
            }));

        } catch (error) {
            console.error('Error fetching project data for group:', groupId, error);
            setToastMessage('Error fetching project data');
            setShowToast(true);
        } finally {
            const newLoadingProjects = new Set(loadingProjects);
            newLoadingProjects.delete(groupId);
            setLoadingProjects(newLoadingProjects);
        }
    };

    // Handle View Report button click
    const handleViewReport = async () => {
        if (!startDate) {
            setToastMessage('Please select a start date');
            setShowToast(true);
            return;
        }

        setIsLoading(true);
        setShowTable(false);
        setExpandedGroups(new Set());
        setProjectData({});

        try {
            let apiUrl = '';
            let params = new URLSearchParams();

            if (reportType === 'summary') {
                // Use User-Wise-Summary endpoint
                apiUrl = '/Reports/User-Wise-Summary';

                // For summary, we filter by supervisor name, not userId
                if (startDate && !endDate) {
                    params.append('date', formatDateForApi(startDate));
                } else if (startDate && endDate) {
                    params.append('startDate', formatDateForApi(startDate));
                    params.append('endDate', formatDateForApi(endDate));
                }

            } else {
                // Use User-Wise endpoint for details
                apiUrl = '/Reports/User-Wise';

                // For details report, we need to pass the userId parameter
                if (selectedUser !== 'all') {
                    const selectedUserObj = users.find(u => u.id === selectedUser);
                    console.log('Selected user object:', selectedUserObj);

                    if (selectedUserObj && selectedUserObj.userId) {
                        params.append('userId', selectedUserObj.userId);
                        console.log('Adding userId to params:', selectedUserObj.userId);
                    } else {
                        console.log('No valid userId found for selected user:', selectedUser);
                        // Try to find the user in allUsers by userName
                        const matchedUser = allUsers.find(u => u.userName === selectedUser);
                        if (matchedUser) {
                            // Try different possible ID fields
                            const userId = matchedUser.id || matchedUser.userId || matchedUser.ID || matchedUser.UserId;
                            if (userId) {
                                params.append('userId', userId);
                                console.log('Adding userId from allUsers:', userId);
                            }
                        }
                    }
                }

                if (startDate && !endDate) {
                    params.append('date', formatDateForApi(startDate));
                } else if (startDate && endDate) {
                    params.append('startDate', formatDateForApi(startDate));
                    params.append('endDate', formatDateForApi(endDate));
                }

                params.append('page', '1');
                params.append('pageSize', '1000');
            }

            const fullUrl = `${apiUrl}?${params.toString()}`;
            console.log('Making API call to:', fullUrl);

            const response = await API.get(fullUrl);
            console.log('API response:', response.data);

            if (reportType === 'summary') {
                let summaryData = response.data.userWiseSummary || [];

                // Filter by selected user if not 'all'
                if (selectedUser !== 'all') {
                    summaryData = summaryData.filter(item => item.supervisor === selectedUser);
                }

                if (summaryData.length === 0) {
                    setToastMessage('No data found for the selected criteria');
                    setShowToast(true);
                } else {
                    setUserReportData(summaryData);
                    setShowTable(true);
                }
            } else {
                const detailsData = response.data.groupWiseSummary || [];
                if (detailsData.length === 0) {
                    setToastMessage('No data found for the selected criteria');
                    setShowToast(true);
                } else {
                    // Transform the data to include process names
                    const transformedData = detailsData.map(item => ({
                        ...item,
                        processNames: item.processIds ? item.processIds.map(id => getProcessName(id)) : []
                    }));
                    setUserReportData(transformedData);
                    setShowTable(true);
                }
            }

        } catch (error) {
            console.error('Error fetching user report data:', error);
            setToastMessage('Error fetching report data. Please try again.');
            setShowToast(true);
        } finally {
            setIsLoading(false);
        }
    };

    // Reset form
    const handleReset = () => {
        setStartDate(new Date().toISOString().split('T')[0]);
        setEndDate('');
        setReportType('summary');
        setSelectedUser('all');
        setShowTable(false);
        setUserReportData([]);
        setExpandedGroups(new Set());
        setProjectData({});
        setLoadingProjects(new Set());
    };

    return (
        <Container fluid>
            <Row>
                <Col xs={12}>
                    {/* Header */}
                    <div className="d-flex justify-content-between align-items-center mb-4">
                        <h4 className="mb-0">
                            <FaUserTie className="me-2" />
                            User Wise Report
                        </h4>
                    </div>

                    {/* Filters Section */}
                    <Card className="mb-4">
                        <Card.Body>
                            <Row className="g-3 align-items-end">
                                <Col xs={12} sm={6} md={3} lg={2}>
                                    <Form.Group>
                                        <Form.Label className="small fw-semibold mb-1">
                                            <FaCalendarAlt className="me-1" size={12} />
                                            From
                                        </Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                            size="sm"
                                            style={{ height: '32px' }}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col xs={12} sm={6} md={3} lg={2}>
                                    <Form.Group>
                                        <Form.Label className="small fw-semibold mb-1">
                                            <FaCalendarAlt className="me-1" size={12} />
                                            To
                                        </Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                            size="sm"
                                            style={{ height: '32px' }}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col xs={12} sm={6} md={3} lg={2}>
                                    <Form.Group>
                                        <Form.Label className="small fw-semibold mb-1">
                                            <FaChartBar className="me-1" size={12} />
                                            Report Type
                                        </Form.Label>
                                        <Form.Select
                                            value={reportType}
                                            onChange={(e) => setReportType(e.target.value)}
                                            size="sm"
                                            style={{ height: '32px' }}
                                        >
                                            <option value="summary">Summary</option>
                                            <option value="details">Details</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col xs={12} sm={6} md={3} lg={2}>
                                    <Form.Group>
                                        <Form.Label className="small fw-semibold mb-1">
                                            <FaUserTie className="me-1" size={12} />
                                            User
                                        </Form.Label>
                                        <Form.Select
                                            value={selectedUser}
                                            onChange={(e) => setSelectedUser(e.target.value)}
                                            size="sm"
                                            style={{ height: '32px' }}
                                            disabled={isLoadingUsers}
                                        >
                                            {isLoadingUsers ? (
                                                <option>Loading users...</option>
                                            ) : (
                                                users.map(user => (
                                                    <option key={user.id} value={user.id}>
                                                        {user.name}
                                                    </option>
                                                ))
                                            )}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col xs={12} sm={6} md={3} lg={2}>
                                    <div className="d-flex gap-2">
                                        <Button
                                            variant="primary"
                                            size="sm"
                                            onClick={handleViewReport}
                                            disabled={isLoading || !startDate}
                                            style={{ height: '32px' }}
                                        >
                                            {isLoading ? (
                                                <>
                                                    <Spinner size="sm" className="me-1" />
                                                    Loading...
                                                </>
                                            ) : (
                                                <>
                                                    <FaSearch className="me-1" size={12} />
                                                    View Report
                                                </>
                                            )}
                                        </Button>
                                        <Button
                                            variant="outline-secondary"
                                            size="sm"
                                            onClick={handleReset}
                                            style={{ height: '32px' }}
                                        >
                                            Reset
                                        </Button>
                                    </div>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>

                    {/* Data Table */}
                    {showTable && (
                        <Card>
                            <Card.Header className="d-flex justify-content-between align-items-center">
                                <h6 className="mb-0">
                                    {reportType === 'summary' ? 'User Summary Report' : 'User Details Report'}
                                </h6>
                                <div className="d-flex gap-2">
                                    <Dropdown>
                                        <Dropdown.Toggle variant="outline-primary" size="sm">
                                            <FaFileExport className="me-1" />
                                        </Dropdown.Toggle>
                                        <Dropdown.Menu>
                                            <Dropdown.Item>
                                                <FaFileExcel className="me-2" />
                                                Export Excel
                                            </Dropdown.Item>
                                            <Dropdown.Item>
                                                <FaFilePdf className="me-2" />
                                                Export PDF
                                            </Dropdown.Item>
                                        </Dropdown.Menu>
                                    </Dropdown>
                                </div>
                            </Card.Header>
                            <Card.Body>
                                {reportType === 'summary' ? (
                                    <Table striped bordered hover size="sm" className="mb-0">
                                        <thead className="table-dark">
                                            <tr style={{ textAlign: 'center' }}>
                                                <th>Supervisor</th>
                                                <th>Catch Count</th>
                                                <th>Total Quantity</th>
                                                <th>Count of Groups</th>
                                                <th>Count of Projects</th>
                                                <th>Count of Lot No</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {userReportData.map((item, index) => (
                                                <tr key={index} style={{ textAlign: 'center' }}>
                                                    <td>{item.supervisor}</td>
                                                    <td>{item.catchCount}</td>
                                                    <td>{item.totalQuantity}</td>
                                                    <td>{item.countOfGroupIds}</td>
                                                    <td>{item.countIfProjectIds}</td>
                                                    <td>{item.countOfLotNo}</td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </Table>
                                ) : (
                                    <Table striped bordered hover size="sm" className="mb-0" style={{ width: '100%' }}>
                                        <thead className="table-dark">
                                            <tr style={{ textAlign: 'center' }}>
                                                <th style={{ width: '20%' }}>Groups</th>
                                                <th style={{ width: '10%' }}>Paper<br/>Catch</th>
                                                <th style={{ width: '10%' }}>Paper<br/>Quantity</th>
                                                <th style={{ width: '10%' }}>Booklet<br/>Catch</th>
                                                <th style={{ width: '10%' }}>Booklet<br/>Quantity</th>
                                                <th style={{ width: '40%' }}>Process</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {userReportData.map((group, index) => (
                                                <React.Fragment key={index}>
                                                    {/* Group Row - Clickable */}
                                                    <tr
                                                        style={{
                                                            cursor: 'pointer',
                                                            backgroundColor: expandedGroups.has(group.groupId) ? '#f8f9fa' : 'inherit'
                                                        }}
                                                        onClick={() => handleGroupExpansion(group.groupId)}
                                                    >
                                                        <td style={{ textAlign: 'left', paddingLeft: '10px' }}>
                                                            {expandedGroups.has(group.groupId) ?
                                                                <FaChevronDown className="me-2" size={12} /> :
                                                                <FaChevronRight className="me-2" size={12} />
                                                            }
                                                            <strong>{group.groupName}</strong>
                                                        </td>
                                                        <td style={{ textAlign: 'center' }}>{group.catchCountInPaper || 0}</td>
                                                        <td style={{ textAlign: 'center' }}>{group.quantitySumInPaper || 0}</td>
                                                        <td style={{ textAlign: 'center' }}>{group.catchCountInBooklet || 0}</td>
                                                        <td style={{ textAlign: 'center' }}>{group.quantitySumInBooklet || 0}</td>
                                                        <td style={{ textAlign: 'left', paddingLeft: '10px' }}>
                                                            {group.processNames && group.processNames.length > 0 ? (
                                                                group.processNames.map((processName, idx) => (
                                                                    <span
                                                                        key={idx}
                                                                        className="badge bg-warning text-dark me-1 mb-1"
                                                                        style={{ fontSize: '10px' }}
                                                                    >
                                                                        {processName}
                                                                    </span>
                                                                ))
                                                            ) : (
                                                                <span className="badge bg-secondary">N/A</span>
                                                            )}
                                                        </td>
                                                    </tr>

                                                    {/* Project Rows - Only shown when group is expanded */}
                                                    {expandedGroups.has(group.groupId) && (
                                                        loadingProjects.has(group.groupId) ? (
                                                            <tr>
                                                                <td colSpan={6} className="text-center py-3">
                                                                    <Spinner size="sm" animation="border" className="me-2" />
                                                                    Loading project data...
                                                                </td>
                                                            </tr>
                                                        ) : (
                                                            projectData[group.groupId] && projectData[group.groupId].length > 0 ? (
                                                                projectData[group.groupId].map((project, projectIndex) => (
                                                                    <React.Fragment key={`project-${projectIndex}`}>
                                                                        {/* Project Row */}
                                                                        <tr style={{ backgroundColor: '#f8f9fa' }}>
                                                                            <td style={{ textAlign: 'left', paddingLeft: '30px' }}>
                                                                                <FaChevronRight className="me-2" size={10} />
                                                                                <strong>{project.projectName}</strong>
                                                                            </td>
                                                                            <td style={{ textAlign: 'center' }}>{project.catchCountInPaper || 0}</td>
                                                                            <td style={{ textAlign: 'center' }}>{project.quantitySumInPaper || 0}</td>
                                                                            <td style={{ textAlign: 'center' }}>{project.catchCountInBooklet || 0}</td>
                                                                            <td style={{ textAlign: 'center' }}>{project.quantitySumInBooklet || 0}</td>
                                                                            <td style={{ textAlign: 'left', paddingLeft: '10px' }}>
                                                                                {project.processNames && project.processNames.length > 0 ? (
                                                                                    project.processNames.map((processName, idx) => (
                                                                                        <span
                                                                                            key={idx}
                                                                                            className="badge bg-info text-dark me-1 mb-1"
                                                                                            style={{ fontSize: '10px' }}
                                                                                        >
                                                                                            {processName}
                                                                                        </span>
                                                                                    ))
                                                                                ) : (
                                                                                    <span className="badge bg-secondary">N/A</span>
                                                                                )}
                                                                            </td>
                                                                        </tr>

                                                                        {/* Sample Lot Rows - You can add lot data here if available */}
                                                                        <tr style={{ backgroundColor: '#f0f0f0' }}>
                                                                            <td style={{ textAlign: 'left', paddingLeft: '50px' }}>
                                                                                Lot 1
                                                                            </td>
                                                                            <td style={{ textAlign: 'center' }}>5</td>
                                                                            <td style={{ textAlign: 'center' }}>6</td>
                                                                            <td style={{ textAlign: 'center' }}>7</td>
                                                                            <td style={{ textAlign: 'center' }}>8</td>
                                                                            <td style={{ textAlign: 'left', paddingLeft: '10px' }}>
                                                                                <span className="badge bg-success text-white me-1" style={{ fontSize: '10px' }}>CTP</span>
                                                                            </td>
                                                                        </tr>
                                                                        <tr style={{ backgroundColor: '#f0f0f0' }}>
                                                                            <td style={{ textAlign: 'left', paddingLeft: '50px' }}>
                                                                                Lot 2
                                                                            </td>
                                                                            <td style={{ textAlign: 'center' }}>4</td>
                                                                            <td style={{ textAlign: 'center' }}>5</td>
                                                                            <td style={{ textAlign: 'center' }}>6</td>
                                                                            <td style={{ textAlign: 'center' }}>7</td>
                                                                            <td style={{ textAlign: 'left', paddingLeft: '10px' }}>
                                                                                <span className="badge bg-warning text-dark me-1" style={{ fontSize: '10px' }}>Proofreading</span>
                                                                            </td>
                                                                        </tr>
                                                                    </React.Fragment>
                                                                ))
                                                            ) : (
                                                                <tr>
                                                                    <td colSpan={6} className="text-center py-3">
                                                                        No project data available for this group
                                                                    </td>
                                                                </tr>
                                                            )
                                                        )
                                                    )}
                                                </React.Fragment>
                                            ))}
                                        </tbody>
                                    </Table>
                                )}
                            </Card.Body>
                        </Card>
                    )}

                    {/* Toast Notification */}
                    <Toast
                        show={showToast}
                        onClose={() => setShowToast(false)}
                        delay={3000}
                        autohide
                        style={{
                            position: 'fixed',
                            top: '20px',
                            right: '20px',
                            zIndex: 9999
                        }}
                    >
                        <Toast.Header>
                            <strong className="me-auto">Notification</strong>
                        </Toast.Header>
                        <Toast.Body>{toastMessage}</Toast.Body>
                    </Toast>
                </Col>
            </Row>
        </Container>
    );
};

export default Userreport;
