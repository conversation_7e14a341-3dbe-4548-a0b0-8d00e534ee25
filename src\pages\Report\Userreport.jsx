import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Form, Button, Table, Card, Spinner, Badge, Dropdown, Toast } from 'react-bootstrap';
import { FaCalendarAlt, FaUserTie, FaChartBar, FaFileExport, FaSearch, FaFilter, FaFileExcel, FaFilePdf, FaChevronRight, FaChevronDown } from 'react-icons/fa';
import API from "../../CustomHooks/MasterApiHooks/api";

const Userreport = () => {
    // State variables
    const [startDate, setStartDate] = useState(() => {
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        return `${yyyy}-${mm}-${dd}`;
    });
    const [endDate, setEndDate] = useState("");
    const [reportType, setReportType] = useState('summary');
    const [selectedUser, setSelectedUser] = useState('all');
    const [users, setUsers] = useState([]);
    const [userReportData, setUserReportData] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [showTable, setShowTable] = useState(false);
    const [showToast, setShowToast] = useState(false);
    const [toastMessage, setToastMessage] = useState('');

    // Format date for API (DD-MM-YYYY format)
    const formatDateForApi = (dateString) => {
        if (!dateString) return '';
        const date = new Date(dateString);
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        return `${day}-${month}-${year}`;
    };

    // Fetch users on component mount
    useEffect(() => {
        const fetchUsers = async () => {
            try {
                const userResponse = await API.get('/User');
                const userOptions = [
                    { id: 'all', name: 'All Users' },
                    ...userResponse.data.map(user => ({
                        id: user.id,
                        name: user.name || user.userName
                    }))
                ];
                setUsers(userOptions);
            } catch (error) {
                console.error('Error fetching users:', error);
                setUsers([{ id: 'all', name: 'All Users' }]);
            }
        };
        fetchUsers();
    }, []);

    // Handle View Report button click
    const handleViewReport = async () => {
        if (!startDate) {
            setToastMessage('Please select a start date');
            setShowToast(true);
            return;
        }

        setIsLoading(true);
        setShowTable(false);

        try {
            let apiUrl = '';
            let params = new URLSearchParams();

            if (reportType === 'summary') {
                // Use User-Wise-Summary endpoint
                apiUrl = '/Reports/User-Wise-Summary';

                if (selectedUser !== 'all' && !isNaN(parseInt(selectedUser))) {
                    params.append('userId', parseInt(selectedUser));
                }

                if (startDate && !endDate) {
                    params.append('date', formatDateForApi(startDate));
                } else if (startDate && endDate) {
                    params.append('startDate', formatDateForApi(startDate));
                    params.append('endDate', formatDateForApi(endDate));
                }

            } else {
                // Use User-Wise endpoint for details
                apiUrl = '/Reports/User-Wise';

                if (selectedUser !== 'all' && !isNaN(parseInt(selectedUser))) {
                    params.append('userId', parseInt(selectedUser));
                }

                if (startDate && !endDate) {
                    params.append('date', formatDateForApi(startDate));
                } else if (startDate && endDate) {
                    params.append('startDate', formatDateForApi(startDate));
                    params.append('endDate', formatDateForApi(endDate));
                }

                params.append('page', '1');
                params.append('pageSize', '1000');
            }

            const response = await API.get(`${apiUrl}?${params.toString()}`);

            if (reportType === 'summary') {
                const summaryData = response.data.userWiseSummary || [];
                if (summaryData.length === 0) {
                    setToastMessage('No data found for the selected criteria');
                    setShowToast(true);
                } else {
                    setUserReportData(summaryData);
                    setShowTable(true);
                }
            } else {
                const detailsData = response.data.groupWiseSummary || [];
                if (detailsData.length === 0) {
                    setToastMessage('No data found for the selected criteria');
                    setShowToast(true);
                } else {
                    setUserReportData(detailsData);
                    setShowTable(true);
                }
            }

        } catch (error) {
            console.error('Error fetching user report data:', error);
            setToastMessage('Error fetching report data. Please try again.');
            setShowToast(true);
        } finally {
            setIsLoading(false);
        }
    };

    // Reset form
    const handleReset = () => {
        setStartDate(new Date().toISOString().split('T')[0]);
        setEndDate('');
        setReportType('summary');
        setSelectedUser('all');
        setShowTable(false);
        setUserReportData([]);
    };

    return (
        <Container fluid>
            <Row>
                <Col xs={12}>
                    {/* Header */}
                    <div className="d-flex justify-content-between align-items-center mb-4">
                        <h4 className="mb-0">
                            <FaUserTie className="me-2" />
                            User Wise Report
                        </h4>
                    </div>

                    {/* Filters Section */}
                    <Card className="mb-4">
                        <Card.Body>
                            <Row className="g-3 align-items-end">
                                <Col xs={12} sm={6} md={3} lg={2}>
                                    <Form.Group>
                                        <Form.Label className="small fw-semibold mb-1">
                                            <FaCalendarAlt className="me-1" size={12} />
                                            From
                                        </Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={startDate}
                                            onChange={(e) => setStartDate(e.target.value)}
                                            size="sm"
                                            style={{ height: '32px' }}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col xs={12} sm={6} md={3} lg={2}>
                                    <Form.Group>
                                        <Form.Label className="small fw-semibold mb-1">
                                            <FaCalendarAlt className="me-1" size={12} />
                                            To
                                        </Form.Label>
                                        <Form.Control
                                            type="date"
                                            value={endDate}
                                            onChange={(e) => setEndDate(e.target.value)}
                                            size="sm"
                                            style={{ height: '32px' }}
                                        />
                                    </Form.Group>
                                </Col>
                                <Col xs={12} sm={6} md={3} lg={2}>
                                    <Form.Group>
                                        <Form.Label className="small fw-semibold mb-1">
                                            <FaChartBar className="me-1" size={12} />
                                            Report Type
                                        </Form.Label>
                                        <Form.Select
                                            value={reportType}
                                            onChange={(e) => setReportType(e.target.value)}
                                            size="sm"
                                            style={{ height: '32px' }}
                                        >
                                            <option value="summary">Summary</option>
                                            <option value="details">Details</option>
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col xs={12} sm={6} md={3} lg={2}>
                                    <Form.Group>
                                        <Form.Label className="small fw-semibold mb-1">
                                            <FaUserTie className="me-1" size={12} />
                                            User
                                        </Form.Label>
                                        <Form.Select
                                            value={selectedUser}
                                            onChange={(e) => setSelectedUser(e.target.value)}
                                            size="sm"
                                            style={{ height: '32px' }}
                                        >
                                            {users.map(user => (
                                                <option key={user.id} value={user.id}>
                                                    {user.name}
                                                </option>
                                            ))}
                                        </Form.Select>
                                    </Form.Group>
                                </Col>
                                <Col xs={12} sm={6} md={3} lg={2}>
                                    <div className="d-flex gap-2">
                                        <Button
                                            variant="primary"
                                            size="sm"
                                            onClick={handleViewReport}
                                            disabled={isLoading || !startDate}
                                            style={{ height: '32px' }}
                                        >
                                            {isLoading ? (
                                                <>
                                                    <Spinner size="sm" className="me-1" />
                                                    Loading...
                                                </>
                                            ) : (
                                                <>
                                                    <FaSearch className="me-1" size={12} />
                                                    View Report
                                                </>
                                            )}
                                        </Button>
                                        <Button
                                            variant="outline-secondary"
                                            size="sm"
                                            onClick={handleReset}
                                            style={{ height: '32px' }}
                                        >
                                            Reset
                                        </Button>
                                    </div>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>

                    {/* Data Table */}
                    {showTable && (
                        <Card>
                            <Card.Header className="d-flex justify-content-between align-items-center">
                                <h6 className="mb-0">
                                    {reportType === 'summary' ? 'User Summary Report' : 'User Details Report'}
                                </h6>
                                <div className="d-flex gap-2">
                                    <Dropdown>
                                        <Dropdown.Toggle variant="outline-primary" size="sm">
                                            <FaFileExport className="me-1" />
                                        </Dropdown.Toggle>
                                        <Dropdown.Menu>
                                            <Dropdown.Item>
                                                <FaFileExcel className="me-2" />
                                                Export Excel
                                            </Dropdown.Item>
                                            <Dropdown.Item>
                                                <FaFilePdf className="me-2" />
                                                Export PDF
                                            </Dropdown.Item>
                                        </Dropdown.Menu>
                                    </Dropdown>
                                </div>
                            </Card.Header>
                            <Card.Body>
                                {reportType === 'summary' ? (
                                    <Table striped bordered hover size="sm" className="mb-0">
                                        <thead className="table-dark">
                                            <tr style={{ textAlign: 'center' }}>
                                                <th>Supervisor</th>
                                                <th>Catch Count</th>
                                                <th>Total Quantity</th>
                                                <th>Count of Groups</th>
                                                <th>Count of Projects</th>
                                                <th>Count of Lot No</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {userReportData.map((item, index) => (
                                                <tr key={index} style={{ textAlign: 'center' }}>
                                                    <td>{item.supervisor}</td>
                                                    <td>{item.catchCount}</td>
                                                    <td>{item.totalQuantity}</td>
                                                    <td>{item.countOfGroupIds}</td>
                                                    <td>{item.countIfProjectIds}</td>
                                                    <td>{item.countOfLotNo}</td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </Table>
                                ) : (
                                    <Table striped bordered hover size="sm" className="mb-0">
                                        <thead className="table-dark">
                                            <tr style={{ textAlign: 'center' }}>
                                                <th>Group Name</th>
                                                <th>Paper Count</th>
                                                <th>Paper Quantity</th>
                                                <th>Booklet Count</th>
                                                <th>Booklet Quantity</th>
                                                <th>Processes</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {userReportData.map((item, index) => (
                                                <tr key={index} style={{ textAlign: 'center' }}>
                                                    <td>{item.groupName}</td>
                                                    <td>{item.catchCountInPaper}</td>
                                                    <td>{item.quantitySumInPaper}</td>
                                                    <td>{item.catchCountInBooklet}</td>
                                                    <td>{item.quantitySumInBooklet}</td>
                                                    <td>
                                                        {item.processIds && item.processIds.length > 0
                                                            ? item.processIds.join(', ')
                                                            : 'N/A'
                                                        }
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </Table>
                                )}
                            </Card.Body>
                        </Card>
                    )}

                    {/* Toast Notification */}
                    <Toast
                        show={showToast}
                        onClose={() => setShowToast(false)}
                        delay={3000}
                        autohide
                        style={{
                            position: 'fixed',
                            top: '20px',
                            right: '20px',
                            zIndex: 9999
                        }}
                    >
                        <Toast.Header>
                            <strong className="me-auto">Notification</strong>
                        </Toast.Header>
                        <Toast.Body>{toastMessage}</Toast.Body>
                    </Toast>
                </Col>
            </Row>
        </Container>
    );
};

export default Userreport;
